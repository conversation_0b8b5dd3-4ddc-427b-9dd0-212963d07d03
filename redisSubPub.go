package main

import (
	"context"
	"fmt"
	"github.com/go-redis/redis/v8"
)
func main() {
	subscriber:=getClient()
	//publisher:=getClient()
	sub:= subscriber.Subscribe(context.Background(),"channel-1")
	channel:= sub.Channel()
	go func() {
		for msg :=range channel {
			fmt.Println(msg.Channel, msg.Payload)
		}
	}()
	subscriber.Subscribe(context.Background(),"channel-2")
	// publisher.Publish(context.Background(),"channel-1","张三")
	// publisher.Publish(context.Background(),"channel-2","李四")
	//time.Sleep(time.Second*10)
	value,_:=subscriber.Get(context.Background(),"一个不存在的key").Result()
	fmt.Println(value)

}

func getClient() *redis.Client {
	opt:= redis.Options{
		Addr:               "10.8.8.117:6379",
		DB:                 0,
	}
	c := redis.NewClient(&opt)
	ping := c.<PERSON>(context.Background())
	if ping.Err() != nil {
		panic(ping.Err())
	}
	return c
}