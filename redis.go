package main

import (
	"context"
	"fmt"
	"gitlab.yc345.tv/backend/utils/client/redis"
	"golang.org/x/sync/errgroup"
	"time"
)

func main() {
	ctx := context.Background()
	key := "course1"
	client1 := redis.NewClient("client1", "redis1")
	client1.Set(ctx, key, "val1", time.Minute*5)
	val, err := client1.Get(ctx, key, nil)
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(val)
	var g errgroup.Group

	client2 := redis.NewClient("client2", "redis2")
	client2.Set(ctx, key, "val2", time.Minute*5)
	val, err = client2.Get(ctx, key, nil)
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(val)
}
