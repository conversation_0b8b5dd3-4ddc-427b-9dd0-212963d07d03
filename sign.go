package main

// 龙华云校签名校验

import (
	"crypto/hmac"
	"crypto/sha1"
	"encoding/hex"
	"fmt"
	Url "net/url"
	"sort"
	"strings"
)

func main() {
	// https://test.yangcong345.com/school/activityH5/longhua?Signature=57788f9dc6c304b9e7c7f584e9ce3469ca5e5a51&Nonce=127867&OrgId=105558149&Timestamp=1642045930&Action=lhyx&Secretid=1
	arg := map[string]interface{}{
		"Action":    "lhyx",
		"SecretId":  "1",
		"OrgId":     "105558149",
		"Timestamp": "1642045930",
		"Nonce":     "127867",
	}
	// Signature=57788f9dc6c304b9e7c7f584e9ce3469ca5e5a51&Nonce=127867&OrgId=105558149&Timestamp=1642045930&Action=lhyx&Secretid=1
	url := "https://test.yangcong345.com/school/activityH5/longhua"
	//Sign2("Mk8IiZ4ygdpKDdZWnjk0KnqL8CL73071", "get", url, arg)
	fmt.Println(arg)
	ret := Sign("Mk8IiZ4ygdpKDdZWnjk0KnqL8CL73071", "POST", url, arg)

	if ret == "57788f9dc6c304b9e7c7f584e9ce3469ca5e5a51" {
		fmt.Println("相等：", ret)
	} else {
		fmt.Println("不相等", ret)
	}
}
func Sign(secretKey, method, url string, params map[string]interface{}) string {
	// 移除请求协议部分
	if strings.HasPrefix(url, "https://") {
		url = strings.Replace(url, "https://", "", 1)
	} else if strings.HasPrefix(url, "http://") {
		url = strings.Replace(url, "http://", "", 1)
	}
	// params 排序
	keys := make([]string, 0)
	for k, _ := range params {
		keys = append(keys, k)
	}
	sort.Strings(keys)
	// 构造待签名字符串
	paramsPairs := make([]string, 0)
	for _, k := range keys {
		paramsPairs = append(paramsPairs, fmt.Sprintf("%s=%v", k, params[k]))
	}
	paramsStr := strings.Join(paramsPairs, "&")
	rawStr := fmt.Sprintf("%s%s?%s", method, url, paramsStr)

	//fmt.Println("preSignStr = ", rawStr)
	// hmac_sha1 签名
	mac := hmac.New(sha1.New, []byte(secretKey))
	mac.Write([]byte(rawStr))
	hash := mac.Sum(nil)
	b16encoded := hex.EncodeToString(hash)
	return Url.QueryEscape(b16encoded)
}

func Sign2(secret_key, method, url string, params map[string]string, body string) (signStr string, err error) {

	// 1. 拼接签名串

	// params 排序
	keys := make([]string, 0)
	for k, _ := range params {
		keys = append(keys, k)
	}
	sort.Strings(keys)

	paramsPairs := make([]string, 0)

	for _, k := range keys {
		paramsPairs = append(paramsPairs, fmt.Sprintf("%s=%s", k, params[k]))
	}

	paramsStr := strings.Join(paramsPairs, "&")

	rawStr := fmt.Sprintf("%s%s?%s", method, url, paramsStr)

	if (method == "POST" || method == "PUT") && len(body) > 0 {
		rawStr += fmt.Sprintf("&Data=%s", body)
	}

	fmt.Println(rawStr)

	// 2. hmac_sha1 签名

	mac := hmac.New(sha1.New, []byte(secret_key))
	mac.Write([]byte(rawStr))
	hash := mac.Sum(nil)
	b16encoded := hex.EncodeToString(hash)
	signStr = Url.QueryEscape(b16encoded)
	return
}
