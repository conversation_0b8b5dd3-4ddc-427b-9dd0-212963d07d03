package main

import (
	"context"
	"time"
	"fmt"
)

func main() {
	for {
		fmt.Println(time.Now())
		ctx, cancel := context.WithTimeout(context.Background(), time.Second)
		var done chan bool
		go do(ctx, done)
		select {
		case <-ctx.Done():
			cancel()
			fmt.Println("超时。。。。。。")
		case <-done:
			fmt.Println("完成了。。。。。。")

		}
	}
}

func do(ctx context.Context, done chan bool) {
	defer func() {
		if err := recover(); err != nil {
			fmt.Println(err)
		}
	}()
	time.Sleep(time.Second * 3)
	fmt.Println("通过")
	done <- true
}
