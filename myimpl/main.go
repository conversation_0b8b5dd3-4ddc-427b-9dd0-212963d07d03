package main

import (
	"github.com/golang-jwt/jwt"
	"fmt"
	"context"
)

type Obj struct {
	ID int64
}

func main() {
	tokeStr := "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjVmZjZjOTc4LTRmOTEtMTFlOS1iZGEzLTRmYTZjZWUwNjczYSIsInJvbGUiOiJhZ2VuY3kiLCJhZ2VuY3lJZCI6IjE3NiIsIm5hbWUiOiLmtYvor5Xku6PnkIbllYYiLCJpc0VtcGxveWVlIjpmYWxzZSwicmVnaW9ucyI6WyI1NDI1MjIiLCI1NDI1MjMiLCI1NDI1MjQiLCI1NDI1MjUiLCI1NDI1MjYiLCI1NDI1MjciLCI1NDA2MDIiLCI1NDA2MjEiLCI1NDA2MjIiLCI1NDA2MjMiLCI1NDA2MjQiLCI1NDA2MjUiLCI1NDA2MjYiLCI1NDA2MjciLCI1NDA2MjgiLCI1NDA2MjkiLCI1NDA2MzAiLCI1NDA2QUIiLCI1NDI1MjEiLCI1NDI1MjEiXSwiaWF0IjoxNjg4MTEyNDAyLCJleHAiOjE2OTA3MDQ0MDJ9.ksCh29lTsauMAogkkSHOO3ROnTdvT5i1sGi2gaSXYuM"
	c := ChannelClaims{}
	_, _ = jwt.ParseWithClaims(tokeStr, &c, func(t *jwt.Token) (i interface{}, err error) { // 解析token
		return []byte(""), nil
	})
	fmt.Println(c.Role)
	ctx := context.Background()
	v := ctx.Value("123")
	fmt.Println(v)
}

type ChannelClaims struct {
	User
	jwt.StandardClaims
}

type User struct {
	ID         string   `json:"id"`
	Role       string   `json:"role"`
	AgencyID   string   `json:"agencyId"`
	Name       string   `json:"name"`
	IsEmployee bool     `json:"isEmployee"`
	Regions    []string `json:"regions"`
}
