package main

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"log"
	"reflect"
	"sort"
)

type JSONData struct {
	Count int                      `json:"count"`
	Rows  []map[string]interface{} `json:"rows"`
}

func main() {
	// 读取两个文件
	channelData, err := readJSONFile("files/channel.txt")
	if err != nil {
		log.Fatalf("读取 channel.txt 失败: %v", err)
	}

	serverData, err := readJSONFile("files/server.txt")
	if err != nil {
		log.Fatalf("读取 server.txt 失败: %v", err)
	}

	// 比较两个文件
	fmt.Println("=== JSON 文件差异比较 ===")
	fmt.Printf("Channel 文件记录数: %d\n", channelData.Count)
	fmt.Printf("Server 文件记录数: %d\n", serverData.Count)
	fmt.Println()

	// 比较 count 字段
	if channelData.Count != serverData.Count {
		fmt.Printf("❌ Count 字段不同: Channel=%d, Server=%d\n", channelData.Count, serverData.Count)
	} else {
		fmt.Printf("✅ Count 字段相同: %d\n", channelData.Count)
	}
	fmt.Println()

	// 比较 rows 数组
	compareRows(channelData.Rows, serverData.Rows)
}

func readJSONFile(filename string) (*JSONData, error) {
	data, err := ioutil.ReadFile(filename)
	if err != nil {
		return nil, err
	}

	var jsonData JSONData
	err = json.Unmarshal(data, &jsonData)
	if err != nil {
		return nil, err
	}

	return &jsonData, nil
}

func compareRows(channelRows, serverRows []map[string]interface{}) {
	fmt.Println("=== Rows 数组比较 ===")
	
	// 检查数组长度
	if len(channelRows) != len(serverRows) {
		fmt.Printf("❌ Rows 数组长度不同: Channel=%d, Server=%d\n", len(channelRows), len(serverRows))
	} else {
		fmt.Printf("✅ Rows 数组长度相同: %d\n", len(channelRows))
	}
	fmt.Println()

	// 比较每个记录
	maxLen := len(channelRows)
	if len(serverRows) > maxLen {
		maxLen = len(serverRows)
	}

	for i := 0; i < maxLen; i++ {
		fmt.Printf("--- 记录 %d ---\n", i+1)
		
		if i >= len(channelRows) {
			fmt.Printf("❌ Channel 文件缺少记录 %d\n", i+1)
			continue
		}
		
		if i >= len(serverRows) {
			fmt.Printf("❌ Server 文件缺少记录 %d\n", i+1)
			continue
		}

		compareRecord(channelRows[i], serverRows[i], i+1)
		fmt.Println()
	}
}

func compareRecord(channelRecord, serverRecord map[string]interface{}, recordIndex int) {
	// 获取所有字段名
	allFields := make(map[string]bool)
	for field := range channelRecord {
		allFields[field] = true
	}
	for field := range serverRecord {
		allFields[field] = true
	}

	// 转换为排序的切片
	var fields []string
	for field := range allFields {
		fields = append(fields, field)
	}
	sort.Strings(fields)

	hasFieldDiff := false
	var fieldDiffs []string

	// 比较每个字段
	for _, field := range fields {
		channelValue, channelExists := channelRecord[field]
		serverValue, serverExists := serverRecord[field]

		if !channelExists && !serverExists {
			continue
		}

		if !channelExists {
			hasFieldDiff = true
			fieldDiffs = append(fieldDiffs, fmt.Sprintf("  - %s: Channel 缺少此字段, Server 有值", field))
			continue
		}

		if !serverExists {
			hasFieldDiff = true
			fieldDiffs = append(fieldDiffs, fmt.Sprintf("  - %s: Server 缺少此字段, Channel 有值", field))
			continue
		}

		// 比较值
		if !deepEqual(channelValue, serverValue) {
			hasFieldDiff = true
			fieldDiffs = append(fieldDiffs, fmt.Sprintf("  - %s: 值不同", field))
			
			// 如果是简单类型，显示具体差异
			if isSimpleType(channelValue) && isSimpleType(serverValue) {
				fieldDiffs = append(fieldDiffs, fmt.Sprintf("    Channel: %v", channelValue))
				fieldDiffs = append(fieldDiffs, fmt.Sprintf("    Server:  %v", serverValue))
			}
		}
	}

	if hasFieldDiff {
		fmt.Printf("❌ 记录 %d 有差异:\n", recordIndex)
		for _, diff := range fieldDiffs {
			fmt.Println(diff)
		}
	} else {
		fmt.Printf("✅ 记录 %d 完全相同\n", recordIndex)
	}
}

func deepEqual(a, b interface{}) bool {
	return reflect.DeepEqual(a, b)
}

func isSimpleType(v interface{}) bool {
	switch v.(type) {
	case string, int, int64, float64, bool, nil:
		return true
	default:
		return false
	}
}

func formatValue(v interface{}) string {
	if v == nil {
		return "null"
	}
	
	switch val := v.(type) {
	case string:
		if len(val) > 50 {
			return fmt.Sprintf("\"%.50s...\"", val)
		}
		return fmt.Sprintf("\"%s\"", val)
	case map[string]interface{}:
		return fmt.Sprintf("{...} (object with %d fields)", len(val))
	case []interface{}:
		return fmt.Sprintf("[...] (array with %d elements)", len(val))
	default:
		return fmt.Sprintf("%v", val)
	}
}
