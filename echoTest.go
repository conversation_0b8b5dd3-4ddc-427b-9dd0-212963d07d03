package main

import (
	"fmt"
	"github.com/labstack/echo/v4"
	"github.com/labstack/echo/v4/middleware"
	"log"
	"time"
)

// 初始化业务需要基础操作，下面是初始化echo，以及中间件
func main() {
	echoWeb := echo.New()
	echoWeb.Debug = true
	echoWeb.Use(middleware.TimeoutWithConfig(middleware.TimeoutConfig{
		Timeout: time.Second,
		OnTimeoutRouteErrorHandler: func(err error, c echo.Context) {
			log.Printf("Timeout:%s", c.Path())
		},
	}))
	echoWeb.Use(middleware.Recover()) // 主要用于拦截panic错误并且在控制台打印错误日志，避免echo程序直接崩溃
	echoWeb.Use(middleware.Logger())  // Logger中间件主要用于打印http请求日志
	echoWeb.GET("/", func(c echo.Context) error {
		fmt.Printf("1111111111=============1111111111")
		t := MyTime{}
		d := Demo{t}
		return c.JSON(200, d)
	})
	echoWeb.Logger.Fatal(echoWeb.Start(":2022"))
}

type MyTime time.Time
type Demo struct {
	T MyTime
}

func (myT MyTime) MarshalText() (data []byte, err error) {
	t := time.Time(myT)
	time.Sleep(2 * time.Second)
	fmt.Println("唤醒----")
	data = []byte(t.Format("2006-01-02 15:04:05"))
	return
}

func (myT *MyTime) UnmarshalText(text []byte) (err error) {
	t := (*time.Time)(myT)
	time.Sleep(2 * time.Second)
	fmt.Println("唤醒+++++++")
	*t, err = time.Parse("2006-01-02 15:04:05", string(text))
	return
}
