<seelog>
  <outputs>
    <filter levels="debug">
      <console formatid="colored_debug"/>
    </filter>
    <filter levels="info">
      <console formatid="colored_info"/>
      <file path="./log/go-channel-order.log" formatid="info"/>
    </filter>
    <filter levels="warn">
      <console formatid="colored_warn"/>
      <file path="./log/go-channel-order.log" formatid="warn"/>
    </filter>
    <filter levels="error">
      <console formatid="colored_error"/>
      <file path="./log/go-channel-order.log" formatid="error"/>
    </filter>
    <filter levels="trace">
      <console formatid="colored_trace"/>
      <file path="./log/go-channel-order.log" formatid="trace"/>
    </filter>
    <filter levels="critical">
      <console formatid="colored_critical"/>
      <file path="./log/go-channel-order.log" formatid="critical"/>
    </filter>
  </outputs>
  <formats>
    <format id="debug" format="[%Level] %Date %Time %Func %Msg%n"/>
    <format id="info" format="[%Level] %Date %Time %Msg%n"/>
    <format id="warn" format="[%Level] %Date %Time  %Msg%n"/>
    <format id="error" format="[%Level] %Date %Time %Func %Msg %n"/>
    <format id="trace" format="[%Level] %Date %Time %Func:%Line %Msg%n"/>
    <format id="critical" format="[%Level] %Date %Time %Msg%n"/>
    <format id="colored_debug" format="%EscM(37;1)[%Level] %Date %Time %Func %Msg%n%EscM(0)"/>
    <format id="colored_info" format="%EscM(32;1)[%Level] %Date %Time %Msg%n%EscM(0)"/>
    <format id="colored_warn" format="%EscM(33;1)[%Level] %Date %Time  %Msg%n%EscM(0)"/>
    <format id="colored_error" format="%EscM(31;1)[%Level] %Date %Time %Func %Msg %n%EscM(0)"/>
    <format id="colored_trace" format="%EscM(35;1)[%Level] %Date %Time %Func:%Line %Msg%n%EscM(0)"/>
    <format id="colored_critical" format="%EscM(34;1)[%Level] %Date %Time %Msg%n%EscM(0)"/>
  </formats>
</seelog>
