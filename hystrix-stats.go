package main

import (
	"github.com/afex/hystrix-go/hystrix"
	metricCollector "github.com/afex/hystrix-go/hystrix/metric_collector"
	"github.com/afex/hystrix-go/plugins"
	"github.com/labstack/gommon/log"
)

func main() {
	hystrix.Go("my_command", func() error {
		// talk to other services
		return nil
	}, nil)
	c, err := plugins.InitializeStatsdCollector(&plugins.StatsdCollectorConfig{
		StatsdAddr: "localhost:8125",
		Prefix:     "myapp.hystrix",
	})
	if err != nil {
		log.Fatalf("could not initialize statsd client: %v", err)
	}

	metricCollector.Registry.Register(c.NewStatsdCollector)
}
