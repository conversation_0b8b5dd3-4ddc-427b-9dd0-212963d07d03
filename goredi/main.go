package main

import (
	"github.com/gomodule/redigo/redis"
	"fmt"
)

// dial wraps DialDefaultServer() with a more suitable function name for examples.
func dial() (redis.Conn, error) {
	return redis.DialDefaultServer()
}

func main() {
	c, err := dial()
	if err != nil {
		fmt.Println(err)
		return
	}
	defer c.Close()

	if _, err = c.Do("SET", "foo", 1); err != nil {
		fmt.Println(err)
		return
	}
	exists, err := redis.Bool(c.Do("EXISTS", "foo"))
	if err != nil {
		fmt.Println(err)
		return
	}

	fmt.Printf("%#v\n", exists)
}
