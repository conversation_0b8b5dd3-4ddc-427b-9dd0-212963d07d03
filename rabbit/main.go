package main

import (
	"context"
	"fmt"
	"time"

	amqp "github.com/rabbitmq/amqp091-go"
	"gitlab.yc345.tv/backend/go-logger/logger"
	client "gitlab.yc345.tv/backend/utils/v2/rabbitmq"
	"go.uber.org/zap"
)

var log logger.ILogger

func init() {
	_log := logger.GetLogger()
	log = _log
}
func main() {
	// forever := make(chan bool)
	// mq, closemq, err := client.NewRabbitmq(&client.AMQPConnectConfig{
	// 	VHost:    "/",
	// 	UserName: "admin",
	// 	Password: "onion345",
	// 	EndPoint: "*********",
	// }, nil)
	// rbtmq-0c04a960db91.rabbitmq.volces.com:5672
	mq, closemq, err := client.NewRabbitmq(&client.AMQPConnectConfig{
		VHost:    "teacher",
		UserName: "admin",
		Password: "onion345",
		EndPoint: "*********:5672",
	}, nil)
	if err != nil {
		log.Error("createRabbitmqFailed", zap.Any("error", err))
	}

	producer, _ := mq.UseOrCreateProducer(&client.ProducerConfig{
		ExchangeName: "",
	})

	consumer, _ := mq.UseOrCreateConsumer(&client.ConsumerConfig{
		ExchangeName: "",
		QueueName:    "test-queue-unexists-v2",
		BindingKey:   "wechat-subscribe",
		QueueConfig: &client.QueueConfig{
			Durable:    true,
			AutoDelete: false,
			Exclusive:  false,
			NoWait:     false,
		},
	}, false)

	go publish(producer, mq.Ctx)
	consumer.ConsumeWithRetry(func(messageID, routingKey string, payload []byte, autoAck bool) error {
		time.Sleep(time.Minute * 31)
		return nil
	})
	min := 0
	seconds := 0
	for {
		fmt.Print("时间：", min, ":", seconds)
		time.Sleep(time.Second)
		if seconds+1 == 60 {
			seconds = 0
			min++
		} else {
			seconds++
		}
	}
	closemq()
}

func publish(producer *client.Producer, ctx context.Context) {
	payload := time.Now().UnixMilli()
	messageId, err := producer.SendMessageWithHeavyConfig(context.Background(), fmt.Sprintf("%d", payload), &client.SendMessageHeavyConfig{
		PublishBody: &amqp.Publishing{
			Headers: map[string]interface{}{},
			// Expiration: "1000",
			Body: []byte(fmt.Sprintf("%d", payload)),
		},
	})
	log.Info("publishResult:", zap.String("messageId", messageId), zap.Any("err", err), zap.Int64("payload", payload))
}
