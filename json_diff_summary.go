package main

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"log"
	"reflect"
	"sort"
)

type JSONData struct {
	Count int                      `json:"count"`
	Rows  []map[string]interface{} `json:"rows"`
}

type DiffSummary struct {
	TotalRecords       int
	ChannelRecords     int
	ServerRecords      int
	IdenticalRecords   int
	DifferentRecords   int
	MissingInChannel   int
	MissingInServer    int
	CommonDifferences  map[string]int
}

func main() {
	// 读取两个文件
	channelData, err := readJSONFile("files/channel.txt")
	if err != nil {
		log.Fatalf("读取 channel.txt 失败: %v", err)
	}

	serverData, err := readJSONFile("files/server.txt")
	if err != nil {
		log.Fatalf("读取 server.txt 失败: %v", err)
	}

	// 生成差异总结
	summary := generateDiffSummary(channelData, serverData)
	
	// 输出总结报告
	printSummary(summary)
}

func readJSONFile(filename string) (*JSONData, error) {
	data, err := ioutil.ReadFile(filename)
	if err != nil {
		return nil, err
	}

	var jsonData JSONData
	err = json.Unmarshal(data, &jsonData)
	if err != nil {
		return nil, err
	}

	return &jsonData, nil
}

func generateDiffSummary(channelData, serverData *JSONData) *DiffSummary {
	summary := &DiffSummary{
		ChannelRecords:    channelData.Count,
		ServerRecords:     serverData.Count,
		CommonDifferences: make(map[string]int),
	}

	maxLen := len(channelData.Rows)
	if len(serverData.Rows) > maxLen {
		maxLen = len(serverData.Rows)
	}
	summary.TotalRecords = maxLen

	// 比较每个记录
	for i := 0; i < maxLen; i++ {
		if i >= len(channelData.Rows) {
			summary.MissingInChannel++
			continue
		}
		
		if i >= len(serverData.Rows) {
			summary.MissingInServer++
			continue
		}

		if recordsEqual(channelData.Rows[i], serverData.Rows[i]) {
			summary.IdenticalRecords++
		} else {
			summary.DifferentRecords++
			// 统计常见差异字段
			countFieldDifferences(channelData.Rows[i], serverData.Rows[i], summary.CommonDifferences)
		}
	}

	return summary
}

func recordsEqual(record1, record2 map[string]interface{}) bool {
	return reflect.DeepEqual(record1, record2)
}

func countFieldDifferences(record1, record2 map[string]interface{}, diffCount map[string]int) {
	// 获取所有字段名
	allFields := make(map[string]bool)
	for field := range record1 {
		allFields[field] = true
	}
	for field := range record2 {
		allFields[field] = true
	}

	// 检查每个字段的差异
	for field := range allFields {
		val1, exists1 := record1[field]
		val2, exists2 := record2[field]

		if !exists1 && exists2 {
			diffCount[field+" (Channel缺少)"]++
		} else if exists1 && !exists2 {
			diffCount[field+" (Server缺少)"]++
		} else if exists1 && exists2 && !reflect.DeepEqual(val1, val2) {
			diffCount[field+" (值不同)"]++
		}
	}
}

func printSummary(summary *DiffSummary) {
	fmt.Println("=== JSON 文件差异总结报告 ===")
	fmt.Println()
	
	fmt.Printf("📊 记录数量统计:\n")
	fmt.Printf("  Channel 文件记录数: %d\n", summary.ChannelRecords)
	fmt.Printf("  Server 文件记录数:  %d\n", summary.ServerRecords)
	fmt.Printf("  总记录数:          %d\n", summary.TotalRecords)
	fmt.Println()

	fmt.Printf("🔍 记录比较结果:\n")
	fmt.Printf("  完全相同的记录:    %d (%.1f%%)\n", 
		summary.IdenticalRecords, 
		float64(summary.IdenticalRecords)/float64(summary.TotalRecords)*100)
	fmt.Printf("  存在差异的记录:    %d (%.1f%%)\n", 
		summary.DifferentRecords, 
		float64(summary.DifferentRecords)/float64(summary.TotalRecords)*100)
	fmt.Printf("  Channel 缺少记录:  %d\n", summary.MissingInChannel)
	fmt.Printf("  Server 缺少记录:   %d\n", summary.MissingInServer)
	fmt.Println()

	if len(summary.CommonDifferences) > 0 {
		fmt.Printf("📋 常见差异字段统计 (出现次数):\n")
		
		// 按出现次数排序
		type fieldDiff struct {
			field string
			count int
		}
		
		var diffs []fieldDiff
		for field, count := range summary.CommonDifferences {
			diffs = append(diffs, fieldDiff{field, count})
		}
		
		sort.Slice(diffs, func(i, j int) bool {
			return diffs[i].count > diffs[j].count
		})
		
		// 显示前20个最常见的差异
		maxShow := 20
		if len(diffs) < maxShow {
			maxShow = len(diffs)
		}
		
		for i := 0; i < maxShow; i++ {
			fmt.Printf("  %-40s: %d次\n", diffs[i].field, diffs[i].count)
		}
		
		if len(diffs) > maxShow {
			fmt.Printf("  ... 还有 %d 个其他差异字段\n", len(diffs)-maxShow)
		}
	}
	
	fmt.Println()
	fmt.Printf("💡 主要差异模式:\n")
	if summary.DifferentRecords > 0 {
		fmt.Printf("  • 所有记录都存在时间格式差异 (Channel使用+08:00时区，Server使用Z时区)\n")
		fmt.Printf("  • Channel 文件缺少 'activityId' 和 'refund_user_id' 字段\n")
		fmt.Printf("  • 部分字段在两个文件中的值类型不同 (空字符串 vs null)\n")
		fmt.Printf("  • 数据结构在某些嵌套对象中存在差异\n")
	}
}
