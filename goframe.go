package main

import "github.com/samber/lo"

func main() {

}

func filterStagePracticeTopics(topics []*Topic, typ int32) []*Topic {
	var topicIndexList []int
	if typ == 1 {
		if len(topics) > 15 {
			for i, step, topicIndex := 0, float64(len(topics))/float64(15), 0; i < 15; i++ {
				topicIndex += int(step)
				topicIndexList = append(topicIndexList, topicIndex)
			}
		} else {
			for i := 0; i <= 15; i++ {
				topicIndexList = append(topicIndexList, i)
			}
		}
	}
	if typ == 2 {
		topic1 := lo.Filter(topics, func(v *Topic, i int) bool { return v.Section == 1 })
		topic2 := lo.Filter(topics, func(v *Topic, i int) bool { return v.Section == 2 })
		if len(topic1)+len(topic2) > 15 {
			for i, step, topicIndex := 1, float64(len(topic1))/float64(5), 0; i <= 15; i++ {
				topicIndex += int(step)
				topicIndexList = append(topicIndexList, topicIndex)
			}
			for i, step, topicIndex := 1, float64(len(topic2))/float64(10), 0; i <= 15; i++ {
				topicIndex += int(step)
				topicIndexList = append(topicIndexList, topicIndex)
			}
		} else {
			for i := 0; i < 15; i++ {
				topicIndexList = append(topicIndexList, i)
			}
			for i := 0; i < 15; i++ {
				topicIndexList = append(topicIndexList, i)
			}
		}
	}
	filterTopics := lo.Filter(topics, func(v *Topic, i int) bool {
		_, ok := lo.Find(topicIndexList, func(t int) bool { return t == i })
		if ok {
			return true
		}
		return false
	})
	return filterTopics
}

type Topic struct {
	Id           string
	SubSectionId string
	Section      int32
}
