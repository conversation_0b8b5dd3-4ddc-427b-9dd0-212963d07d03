name: learn-record
port: 80

database:
  database: catelynStark
  user: postgres
  password: teacherschoolpg94
  host: **********
  port: 5436
eddardstarkdb:
  database: eddardStark
  user: postgres
  password: teacherschoolpg94
  host: **********
  port: 5436
previewdb:
  database: arya_stark
  host: **********
  user: postgres
  password: teacherschoolpg94
  port: 5436
redis1:
  addr: **********:6379
  db: 0
  keyPrefix: "catelynstark#"
redis2:
  addr: **********:6379
  db: 0
  keyPrefix: "catelynstark#"

rabbitMQ:
  problem-statistics:
    url: "amqp-cn-v0h1k9ayi001.mq-amqp.cn-beijing-327844-a.aliyuncs.com"
    instanceId: "amqp-cn-v0h1k9ayi001"
    exchange: "problem-statistics-ex"
    exchangeType: "direct"
    queue: "problem-statistics-q"
    key: "problem-statistics.key"
    tag: "problem-statistics.tag"
    vhost: "yc-test"
    user: MjphbXFwLWNuLXYwaDFrOWF5aTAwMTpMVEFJNEdBWTJwTmplcUpmald0WWZXUkE=
    password: MTkxQkFDNTk2NTBCNUE5M0Y4NDVCNzlGQTRFNTg4OEY5ODcxNzRGNDoxNjA1Nzc2OTc5OTQ3
parentmsgconfig:
  "appId": "wxa95461a752c77e5d"
  "homeworkCreate": "q6I4pWui3PdOKSMLnPwgxJrOH6FTWk3waqY3l_iVznM"
  "homeworkFinish": "sQpb-xIk4OilnEvrTfx99UbOzYmURTP2825L6-2RXFY"
  "homeworkExpired": "AzfI3YM8d9pWn9yZOyivhp-1_u5fFEesl1PjA5IB7lQ"
