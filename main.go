package main

import (
	"github.com/atopx/libreoffice"
)

func main() {
	const (
		defaultLibreOfficePath = "/usr/lib/libreoffice/program"
		sampleDocument         = "testdata/sample.docx"
		saveDocumentPath       = "/tmp/out.pdf"
		saveDocumentFormat     = "pdf"
	)
	office, err := libreoffice.NewOffice(defaultLibreOfficePath)
	if err != nil {
		panic(err)
	}
	document, _ := office.LoadDocument(sampleDocument)
	err = document.SaveAs(saveDocumentPath, saveDocumentFormat, "")
	if err != nil {
		panic(err)
	}

}
