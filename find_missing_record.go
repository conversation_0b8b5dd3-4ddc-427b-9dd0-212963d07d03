package main

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"log"
)

type JSONData struct {
	Count int                      `json:"count"`
	Rows  []map[string]interface{} `json:"rows"`
}

func main() {
	// 读取两个文件
	channelData, err := readJSONFile("files/channel.txt")
	if err != nil {
		log.Fatalf("读取 channel.txt 失败: %v", err)
	}

	serverData, err := readJSONFile("files/server.txt")
	if err != nil {
		log.Fatalf("读取 server.txt 失败: %v", err)
	}

	fmt.Printf("Channel 文件记录数: %d\n", len(channelData.Rows))
	fmt.Printf("Server 文件记录数: %d\n", len(serverData.Rows))
	fmt.Println()

	// 找到缺少的记录
	if len(channelData.Rows) > len(serverData.Rows) {
		missingIndex := len(serverData.Rows)
		fmt.Printf("Server 文件缺少第 %d 条记录 (索引 %d):\n", missingIndex+1, missingIndex)
		fmt.Println()
		
		// 输出缺少的记录
		missingRecord := channelData.Rows[missingIndex]
		jsonBytes, err := json.MarshalIndent(missingRecord, "", "    ")
		if err != nil {
			log.Fatalf("序列化 JSON 失败: %v", err)
		}
		
		fmt.Println("缺少的记录 JSON 数据:")
		fmt.Println(string(jsonBytes))
		
		// 输出一些关键信息
		fmt.Println()
		fmt.Println("关键字段信息:")
		if payId, ok := missingRecord["pay_id"]; ok {
			fmt.Printf("  pay_id: %v\n", payId)
		}
		if studentName, ok := missingRecord["studentName"]; ok {
			fmt.Printf("  studentName: %v\n", studentName)
		}
		if orderId, ok := missingRecord["orderId"]; ok {
			fmt.Printf("  orderId: %v\n", orderId)
		}
		if createdDate, ok := missingRecord["created_date"]; ok {
			fmt.Printf("  created_date: %v\n", createdDate)
		}
		if refundDate, ok := missingRecord["refundDate"]; ok {
			fmt.Printf("  refundDate: %v\n", refundDate)
		}
	} else {
		fmt.Println("Channel 文件记录数不大于 Server 文件，没有缺少的记录")
	}
}

func readJSONFile(filename string) (*JSONData, error) {
	data, err := ioutil.ReadFile(filename)
	if err != nil {
		return nil, err
	}

	var jsonData JSONData
	err = json.Unmarshal(data, &jsonData)
	if err != nil {
		return nil, err
	}

	return &jsonData, nil
}
