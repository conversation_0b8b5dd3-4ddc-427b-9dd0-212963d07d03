package main

import (
	"fmt"
	"github.com/gocarina/gocsv"
	"net/http"
	"os"
)

type PathInfo struct {
	Path     string `csv:"path"`
	SendTime int64  `csv:"time"`
}

/*
  host: *********
  user: teacher
  password: unitedtea
  database: "teachingResource"
  port: 2014
*/

func main() {
	defer func() {
		if err := recover(); err != nil {
			fmt.Println("defer", err)
		}
	}()
	clientsFile, err := os.OpenFile("data.csv", os.O_RDWR|os.O_CREATE, os.ModePerm)
	if err != nil {
		panic(err)
	}
	defer clientsFile.Close()

	paths := []*PathInfo{}
	if err := gocsv.UnmarshalFile(clientsFile, &paths); err != nil { // Load clients from file
		panic(err)
	}
	fmt.Println("总数：", len(paths))
	pathChan := make(chan string, 1)
	go func() {
		for {
			ur := <-path<PERSON>han
			go send(ur)
		}
	}()
	for i := 0; i < len(paths); i++ {
		p := paths[i]
		url := fmt.Sprintf("http://localhost:3000%s", p.Path)
		//pathChan <- url
		code, err := send(url)
		if err != nil {
			fmt.Println("err：", err)
			panic("err不为空")
		}
		fmt.Println(i, "===", code)
	}
	select {}
}
func send(url string) (int, error) {
	c := http.Client{}
	resp, err := c.Get(url)
	if err != nil {
		return 0, err
	}

	return resp.StatusCode, err
}
