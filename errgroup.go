package main

import (
	"encoding/json"
	"fmt"
)

func main() {
	str := `{
  "_id": "47076ea0-01f8-11ed-b6a3-592daee3c055",
  "id": "47076ea0-01f8-11ed-b6a3-592daee3c055",
  "name": "测试班级",
  "ref": "901148",
  "tenantId": null,
  "bulkCount": 0,
  "schoolId": "5a439b1feae844551fed0fb8",
  "groupId": "4704fda0-01f8-11ed-b6a3-592daee3c055",
  "state": "alive",
  "type": "admin",
  "joinRole": "free",
  "changeTime": null,
  "enrollmentYear": "2022",
  "schoolSystem": 3,
  "graduationTime": "2025-08-24T16:00:00.000Z",
  "source": null,
  "creator": "5def6b2c060efa00015392dc",
  "isDeleted": false,
  "createdTime": "2022-07-12T15:35:37.354Z",
  "updatedTime": "2022-07-12T15:35:37.354Z",
  "owners": [
    {
      "id": "6486ff5009f5540001510367",
      "name": "已注销6492f31f61aabd000194ac8c",
      "phone": null,
      "email": null,
      "nickname": "已注销",
      "nicknameUpdateTime": "2023-06-15T12:09:58.707061+08:00",
      "nicknameApproveSubmitTime": null,
      "pwIterations": 10,
      "channel": "appstore",
      "type": "signup",
      "registEntranceId": "02",
      "os": "ios",
      "role": "teacher",
      "countryCode": "CN",
      "onionId": "69731625",
      "regionCode": "",
      "school_id": 226869,
      "schoolYear": "",
      "schoolYear54or63": "",
      "realIdentity": "teacher",
      "realName": "已注销",
      "gender": "",
      "verifiedByPhone": true,
      "registAppVersion": "1.78.1",
      "activateDate": null,
      "thirdPartyOauths": null,
      "attribution": "b",
      "attributionUpdateTime": 1686568789,
      "createTime": "2023-06-12T19:19:44.27403+08:00",
      "updateTime": "2023-06-21T20:54:55+08:00",
      "value": "",
      "_id": "6486ff5009f5540001510367",
      "isActivated": true,
      "userName": "已注销"
    },
    {
      "id": "6482b62b76476400017a1aa7",
      "name": "已注销64869aa740b4890001fbf6d1",
      "phone": null,
      "email": null,
      "nickname": "已注销",
      "nicknameUpdateTime": "2023-06-09T13:19:49.535512+08:00",
      "nicknameApproveSubmitTime": null,
      "pwIterations": 10,
      "channel": "shadow",
      "type": "signup",
      "registEntranceId": "51",
      "os": "mac",
      "role": "teacher",
      "countryCode": "CN",
      "onionId": "19705734",
      "regionCode": "",
      "school_id": 226869,
      "schoolYear": "",
      "schoolYear54or63": "",
      "realIdentity": "teacher",
      "realName": "已注销",
      "gender": "",
      "verifiedByPhone": true,
      "registAppVersion": "",
      "activateDate": null,
      "thirdPartyOauths": null,
      "attribution": "b",
      "attributionUpdateTime": 1686287920,
      "createTime": "2023-06-09T13:18:35.72031+08:00",
      "updateTime": "2023-06-12T12:10:15+08:00",
      "value": "",
      "_id": "6482b62b76476400017a1aa7",
      "isActivated": true,
      "userName": "已注销"
    },
    {
      "id": "623825d2c292e900017232dd",
      "name": "1647846866552_@@_18800001023",
      "phone": "18800001023",
      "email": null,
      "nickname": "23",
      "nicknameUpdateTime": "2023-04-10T03:46:19.366293+08:00",
      "nicknameApproveSubmitTime": null,
      "pwIterations": 10,
      "channel": "shadow",
      "type": "signup",
      "registEntranceId": "51",
      "os": "mac",
      "role": "teacher",
      "countryCode": "CN",
      "onionId": "77132280",
      "regionCode": "",
      "school_id": 226869,
      "schoolYear": "",
      "schoolYear54or63": "",
      "realIdentity": "teacher",
      "realName": "",
      "gender": "",
      "verifiedByPhone": true,
      "registAppVersion": "",
      "activateDate": null,
      "thirdPartyOauths": null,
      "attribution": "b",
      "attributionUpdateTime": 1647846868,
      "createTime": "2022-03-21T15:14:26.565249+08:00",
      "updateTime": "2023-04-10T03:46:19.49392+08:00",
      "value": "",
      "_id": "623825d2c292e900017232dd",
      "isActivated": true,
      "userName": "23"
    },
    {
      "id": "5f70ba8249cfa400012cd0a1",
      "name": "15892000000_@@_1601223298766",
      "phone": "15892000000",
      "email": null,
      "nickname": "测试00001",
      "nicknameUpdateTime": null,
      "nicknameApproveSubmitTime": null,
      "pwIterations": 10,
      "channel": "admianRoom",
      "type": "batch",
      "registEntranceId": "33",
      "os": "windows",
      "role": "teacher",
      "countryCode": "CN",
      "onionId": "34192409",
      "regionCode": "",
      "school_id": 226869,
      "schoolYear": "",
      "schoolYear54or63": "",
      "realIdentity": "teacher",
      "realName": "",
      "gender": "",
      "verifiedByPhone": false,
      "registAppVersion": "",
      "activateDate": "2020-11-10T17:58:15.673694+08:00",
      "thirdPartyOauths": null,
      "attribution": "b",
      "attributionUpdateTime": 1601232584,
      "createTime": "2020-09-28T00:14:58.781651+08:00",
      "updateTime": "2021-09-29T12:29:12.612325+08:00",
      "value": "",
      "_id": "5f70ba8249cfa400012cd0a1",
      "isActivated": true,
      "userName": "测试00001"
    }
  ],
  "members": [
    {
      "id": "6492f340f432cf0001f2e089",
      "name": "已注销64a402a8a05d9800010c864b",
      "phone": null,
      "email": null,
      "nickname": "已注销",
      "nicknameUpdateTime": null,
      "nicknameApproveSubmitTime": null,
      "pwIterations": 10,
      "channel": "AppStore",
      "type": "signup",
      "registEntranceId": "01",
      "os": "ios",
      "role": "student",
      "countryCode": "CN",
      "onionId": "89769796",
      "regionCode": "",
      "school_id": 226869,
      "schoolYear": "八年级",
      "schoolYear54or63": "sixThree",
      "realIdentity": "student",
      "realName": "已注销",
      "gender": "",
      "verifiedByPhone": true,
      "registAppVersion": "7.37.0",
      "activateDate": null,
      "thirdPartyOauths": null,
      "attribution": "b",
      "attributionUpdateTime": 1687948421,
      "createTime": "2023-06-21T20:55:28.805279+08:00",
      "updateTime": "2024-08-15T11:37:53.182077+08:00",
      "value": "",
      "_id": "6492f340f432cf0001f2e089",
      "isActivated": true,
      "userName": "已注销",
      "enterRoomTime": "2023-06-21T12:58:24.029Z"
    },
    {
      "id": "5f86cfbf7ad5570001120eb2",
      "name": "1602670527295_@@_18390205525",
      "phone": "18390205525",
      "email": "<EMAIL>",
      "nickname": "软软糯米糍",
      "nicknameUpdateTime": "2024-11-20T22:29:25.563389+08:00",
      "nicknameApproveSubmitTime": "2024-11-20T22:29:25.563389+08:00",
      "pwIterations": 10,
      "channel": "bdsem37",
      "type": "signup",
      "registEntranceId": "01",
      "os": "android",
      "role": "student",
      "countryCode": "CN",
      "onionId": "64153644",
      "regionCode": "",
      "school_id": 226869,
      "schoolYear": "六年级",
      "schoolYear54or63": "fiveFour",
      "realIdentity": "student",
      "realName": "伦杰",
      "gender": "female",
      "verifiedByPhone": true,
      "registAppVersion": "5.37.0",
      "activateDate": null,
      "thirdPartyOauths": [
        {
          "userId": "5f86cfbf7ad5570001120eb2",
          "type": "qq",
          "openId": "A1A4B895B934C4FDC3DC02ACB0BDC620",
          "unionId": "UID_B4B403C1D5073D4B605AA4A7F3D6C9B4",
          "createTime": "2021-04-15T18:35:58.869169+08:00",
          "updateTime": "2021-04-15T18:35:58.869169+08:00"
        },
        {
          "userId": "5f86cfbf7ad5570001120eb2",
          "type": "huawei",
          "openId": "MDFAMTAyODU5NTRANTc4NDYzNTU3YTc5NmE0NDA0N2ViaZTc4NGZmM2QzMzBAOGQ1YmMzMmMxOGIzYWJkYTJlMmRkMzVjMGY3OGNjZDNjZGIwZjFjODVlMjg5YzliaOGIyMw",
          "unionId": null,
          "createTime": "2022-07-16T15:43:54.776542+08:00",
          "updateTime": "2022-07-16T15:43:54.776542+08:00"
        },
        {
          "userId": "5f86cfbf7ad5570001120eb2",
          "type": "weixin",
          "openId": "o4314twNnVSg025Gef0vmLCHHmww",
          "unionId": "o8Xt4t47d78b__BC2e4Z58zQcruc",
          "createTime": "2021-04-15T18:36:13.031308+08:00",
          "updateTime": "2021-04-15T18:36:13.031308+08:00"
        }
      ],
      "attribution": "b",
      "attributionUpdateTime": 1705405157,
      "createTime": "2020-10-14T18:15:27.311569+08:00",
      "updateTime": "2024-11-21T12:40:35.260227+08:00",
      "value": "normal",
      "_id": "5f86cfbf7ad5570001120eb2",
      "isActivated": true,
      "userName": "伦杰",
      "enterRoomTime": "2023-03-17T14:01:09.985Z"
    },
    {
      "id": "64143159b5e81900014d1032",
      "name": "已注销6438f4226fb36b000177f795",
      "phone": null,
      "email": null,
      "nickname": "已注销",
      "nicknameUpdateTime": null,
      "nicknameApproveSubmitTime": null,
      "pwIterations": 10,
      "channel": "shadow",
      "type": "signup",
      "registEntranceId": "51",
      "os": "mac",
      "role": "student",
      "countryCode": "CN",
      "onionId": "89333095",
      "regionCode": "",
      "school_id": 226869,
      "schoolYear": "八年级",
      "schoolYear54or63": "sixThree",
      "realIdentity": "student",
      "realName": "已注销",
      "gender": "",
      "verifiedByPhone": true,
      "registAppVersion": "",
      "activateDate": null,
      "thirdPartyOauths": null,
      "attribution": "b",
      "attributionUpdateTime": 1679456362,
      "createTime": "2023-03-17T17:22:33.885214+08:00",
      "updateTime": "2024-08-15T11:38:07.438047+08:00",
      "value": "",
      "_id": "64143159b5e81900014d1032",
      "isActivated": true,
      "userName": "已注销",
      "enterRoomTime": "2023-03-22T03:38:33.997Z"
    },
    {
      "id": "635f940c1056c5000155493e",
      "name": "1667208204378_@@_15321331085",
      "phone": null,
      "email": null,
      "nickname": "153****1085",
      "nicknameUpdateTime": null,
      "nicknameApproveSubmitTime": null,
      "pwIterations": 10,
      "channel": "shadow",
      "type": "signup",
      "registEntranceId": "51",
      "os": "windows",
      "role": "student",
      "countryCode": "CN",
      "onionId": "78544417",
      "regionCode": "",
      "school_id": 226869,
      "schoolYear": "八年级",
      "schoolYear54or63": "",
      "realIdentity": "student",
      "realName": "里子测试111",
      "gender": "",
      "verifiedByPhone": true,
      "registAppVersion": "",
      "activateDate": null,
      "thirdPartyOauths": null,
      "attribution": "b",
      "attributionUpdateTime": 1667208330,
      "createTime": "2022-10-31T17:23:24.390889+08:00",
      "updateTime": "2024-08-15T11:38:23.240915+08:00",
      "value": "",
      "_id": "635f940c1056c5000155493e",
      "isActivated": true,
      "userName": "里子测试111",
      "enterRoomTime": "2022-10-31T09:25:20.846Z"
    },
    {
      "id": "63893a27efd6e20001df57f5",
      "name": "已注销640efcce28842f0001879122",
      "phone": null,
      "email": null,
      "nickname": "已注销",
      "nicknameUpdateTime": null,
      "nicknameApproveSubmitTime": null,
      "pwIterations": 10,
      "channel": "AppStore",
      "type": "signup",
      "registEntranceId": "01",
      "os": "ios",
      "role": "student",
      "countryCode": "CN",
      "onionId": "98754337",
      "regionCode": "",
      "school_id": 226869,
      "schoolYear": "八年级",
      "schoolYear54or63": "sixThree",
      "realIdentity": "student",
      "realName": "已注销",
      "gender": "",
      "verifiedByPhone": true,
      "registAppVersion": "7.24.1",
      "activateDate": null,
      "thirdPartyOauths": null,
      "attribution": "b",
      "attributionUpdateTime": 1678175541,
      "createTime": "2022-12-02T07:35:03.929688+08:00",
      "updateTime": "2024-08-15T11:38:15.604124+08:00",
      "value": "",
      "_id": "63893a27efd6e20001df57f5",
      "isActivated": true,
      "userName": "已注销",
      "enterRoomTime": "2023-03-07T07:52:16.153Z"
    }
  ],
  "memberCount": 5
}`
	res := GetRoomByRefResp{}
	err := json.Unmarshal([]byte(str), &res)
	if err != nil {
		panic(err)
	}
	fmt.Println(res.Id)
}

type GetRoomByRefResp struct {
	Idx            string        `protobuf:"bytes,1,opt,name=idx,json=_id,proto3" json:"idx,omitempty"`
	Id             string        `protobuf:"bytes,2,opt,name=id,proto3" json:"id,omitempty"`
	Name           string        `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Ref            string        `protobuf:"bytes,4,opt,name=ref,proto3" json:"ref,omitempty"`
	TenantId       string        `protobuf:"bytes,5,opt,name=tenantId,proto3" json:"tenantId,omitempty"`
	BulkCount      uint32        `protobuf:"varint,6,opt,name=bulkCount,proto3" json:"bulkCount,omitempty"`
	SchoolId       string        `protobuf:"bytes,7,opt,name=schoolId,proto3" json:"schoolId,omitempty"`
	GroupId        string        `protobuf:"bytes,8,opt,name=groupId,proto3" json:"groupId,omitempty"`
	State          string        `protobuf:"bytes,9,opt,name=state,proto3" json:"state,omitempty"`
	Type           string        `protobuf:"bytes,10,opt,name=type,proto3" json:"type,omitempty"`
	JoinRole       string        `protobuf:"bytes,11,opt,name=joinRole,proto3" json:"joinRole,omitempty"`
	ChangeTime     string        `protobuf:"bytes,12,opt,name=changeTime,proto3" json:"changeTime,omitempty"`
	EnrollmentYear string        `protobuf:"bytes,13,opt,name=enrollmentYear,proto3" json:"enrollmentYear,omitempty"`
	SchoolSystem   uint32        `protobuf:"varint,14,opt,name=schoolSystem,proto3" json:"schoolSystem,omitempty"`
	GraduationTime string        `protobuf:"bytes,15,opt,name=graduationTime,proto3" json:"graduationTime,omitempty"`
	Source         string        `protobuf:"bytes,16,opt,name=source,proto3" json:"source,omitempty"`
	Creator        string        `protobuf:"bytes,17,opt,name=creator,proto3" json:"creator,omitempty"`
	IsDeleted      bool          `protobuf:"varint,18,opt,name=isDeleted,proto3" json:"isDeleted,omitempty"`
	CreatedTime    string        `protobuf:"bytes,19,opt,name=createdTime,proto3" json:"createdTime,omitempty"`
	UpdatedTime    string        `protobuf:"bytes,20,opt,name=updatedTime,proto3" json:"updatedTime,omitempty"`
	Members        []*RoomMember `protobuf:"bytes,21,rep,name=members,proto3" json:"members,omitempty"`
}

type RoomMember struct {
	Id                        string  `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                      string  `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Phone                     string  `protobuf:"bytes,3,opt,name=phone,proto3" json:"phone,omitempty"`
	Email                     string  `protobuf:"bytes,4,opt,name=email,proto3" json:"email,omitempty"`
	Nickname                  string  `protobuf:"bytes,5,opt,name=nickname,proto3" json:"nickname,omitempty"`
	NicknameUpdateTime        string  `protobuf:"bytes,6,opt,name=nicknameUpdateTime,proto3" json:"nicknameUpdateTime,omitempty"`
	NicknameApproveSubmitTime string  `protobuf:"bytes,7,opt,name=nicknameApproveSubmitTime,proto3" json:"nicknameApproveSubmitTime,omitempty"`
	PwIterations              uint32  `protobuf:"varint,8,opt,name=pwIterations,proto3" json:"pwIterations,omitempty"`
	Channel                   string  `protobuf:"bytes,9,opt,name=channel,proto3" json:"channel,omitempty"`
	Type                      string  `protobuf:"bytes,10,opt,name=type,proto3" json:"type,omitempty"`
	RegistEntranceId          string  `protobuf:"bytes,11,opt,name=registEntranceId,proto3" json:"registEntranceId,omitempty"`
	Os                        string  `protobuf:"bytes,12,opt,name=os,proto3" json:"os,omitempty"`
	Role                      string  `protobuf:"bytes,13,opt,name=role,proto3" json:"role,omitempty"`
	CountryCode               string  `protobuf:"bytes,14,opt,name=countryCode,proto3" json:"countryCode,omitempty"`
	OnionId                   string  `protobuf:"bytes,15,opt,name=onionId,proto3" json:"onionId,omitempty"`
	RegionCode                string  `protobuf:"bytes,16,opt,name=regionCode,proto3" json:"regionCode,omitempty"`
	SchoolId                  uint32  `protobuf:"varint,17,opt,name=school_id,json=schoolId,proto3" json:"school_id,omitempty"`
	SchoolYear                string  `protobuf:"bytes,18,opt,name=schoolYear,proto3" json:"schoolYear,omitempty"`
	SchoolYear54Or63          string  `protobuf:"bytes,19,opt,name=schoolYear54or63,proto3" json:"schoolYear54or63,omitempty"`
	RealIdentity              string  `protobuf:"bytes,20,opt,name=realIdentity,proto3" json:"realIdentity,omitempty"`
	RealName                  string  `protobuf:"bytes,21,opt,name=realName,proto3" json:"realName,omitempty"`
	Gender                    string  `protobuf:"bytes,22,opt,name=gender,proto3" json:"gender,omitempty"`
	VerifiedByPhone           bool    `protobuf:"varint,23,opt,name=verifiedByPhone,proto3" json:"verifiedByPhone,omitempty"`
	RegistAppVersion          string  `protobuf:"bytes,24,opt,name=registAppVersion,proto3" json:"registAppVersion,omitempty"`
	ActivateDate              string  `protobuf:"bytes,25,opt,name=activateDate,proto3" json:"activateDate,omitempty"`
	ThirdPartyOauths          []Oauth `protobuf:"bytes,26,opt,name=thirdPartyOauths,proto3" json:"thirdPartyOauths,omitempty"`
	Attribution               string  `protobuf:"bytes,27,opt,name=attribution,proto3" json:"attribution,omitempty"`
	AttributionUpdateTime     uint32  `protobuf:"varint,28,opt,name=attributionUpdateTime,proto3" json:"attributionUpdateTime,omitempty"`
	CreateTime                string  `protobuf:"bytes,29,opt,name=createTime,proto3" json:"createTime,omitempty"`
	UpdateTime                string  `protobuf:"bytes,30,opt,name=updateTime,proto3" json:"updateTime,omitempty"`
	Value                     string  `protobuf:"bytes,31,opt,name=value,proto3" json:"value,omitempty"`
	ObjectId                  string  `protobuf:"bytes,32,opt,name=objectId,json=_id,proto3" json:"objectId,omitempty"`
	IsActivated               bool    `protobuf:"varint,33,opt,name=isActivated,proto3" json:"isActivated,omitempty"`
	UserName                  string  `protobuf:"bytes,34,opt,name=userName,proto3" json:"userName,omitempty"`
	EnterRoomTime             string  `protobuf:"bytes,35,opt,name=enterRoomTime,proto3" json:"enterRoomTime,omitempty"`
}

// "thirdPartyOauths": [
//        {
//          "userId": "5f86cfbf7ad5570001120eb2",
//          "type": "qq",
//          "openId": "A1A4B895B934C4FDC3DC02ACB0BDC620",
//          "unionId": "UID_B4B403C1D5073D4B605AA4A7F3D6C9B4",
//          "createTime": "2021-04-15T18:35:58.869169+08:00",
//          "updateTime": "2021-04-15T18:35:58.869169+08:00"
//        }
type Oauth struct {
	UserId     string `json:"userId"`
	Type       string `json:"type"`
	OpenId     string `json:"openId"`
	UnionId    string `json:"unionId"`
	CreateTime string `json:"createTime"`
	UpdateTime string `json:"updateTime"`
}
