package main

import (
	"gorm.io/gorm/logger"
	"gorm.io/gorm"
	"gorm.io/driver/postgres"
	"fmt"
	"github.com/google/uuid"
)

type User struct {
	ID   string
	Name string
}

func main() {
	dns := fmt.Sprintf(
		"host=%s user=%s password=%s dbname=%s port=%d sslmode=disable TimeZone=Asia/Shanghai",
		"**********",
		"postgres",
		"teacherschoolpg94",
		"teacher_activity",
		5436,
	)
	// log.Println(dns)
	db, _ := gorm.Open(postgres.Open(dns), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	})
	u := User{
		ID:   uuid.New().String(),
		Name: "张三",
	}
	err := db.Where(User{Name: "张三"}).FirstOrCreate(&u).Error
	fmt.Println(err)
}
