package main

import (
	"context"
	"github.com/google/uuid"
	"github.com/cihub/seelog"
)

var key = "traceID"

func main() {
	// ctx := context.Background()
	// ctx = appendTraceID(ctx)
	// fmt.Println(ctx.Value(key))
	sql := "select"
	argMap := map[string]interface{}{
		"name": "张三",
		"age":  10,
	}
	logger, err := seelog.LoggerFromConfigAsFile("seelog.xml")

	if err != nil {
		seelog.Critical("err parsing config log file", err)
		return
	}
	seelog.ReplaceLogger(logger)
	seelog.Errorf("getAchievementByBase#SQL:%s#args：%s", sql, argMap)
}

func appendTraceID(ctx context.Context) context.Context {
	return context.WithValue(ctx, key, uuid.New().String())
}
