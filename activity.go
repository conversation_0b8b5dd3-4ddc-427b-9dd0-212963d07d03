package main

import (
	"encoding/json"
	"fmt"
)

func main() {
	mqBody := new(ActivityMQBase)
	s := `{"requestInfo":null,"extra":{"userId":"61d05be94d245b00013fed7e"},"scene":"login"}`
	err := json.Unmarshal([]byte(s), mqBody)
	if err != nil {
		panic(err)
		return
	}
	fmt.Println(mqBody)
	userId := mqBody.Extra["userId"].(string)
	fmt.Println(userId)
}

type ActivityMQBase struct {
	Scene string                 `json:"scene"` // 场景，signup(注册)、login(登录)、approveAuth(教师认证通过)
	Extra map[string]interface{} `json:"extra"` // 扩展信息
}
