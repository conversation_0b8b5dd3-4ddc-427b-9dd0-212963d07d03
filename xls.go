package main

import (
	"fmt"
	"github.com/xuri/excelize/v2"
)

func main() {
	f := excelize.NewFile()

	blueStyle := excelize.Style{
		Alignment: &excelize.Alignment{
			Horizontal: "center",
			Vertical:   "center",
		},
		Font: &excelize.Font{Color: "#FFFFFF", VertAlign: "center"},
		Fill: excelize.Fill{Color: []string{"#1765E4"}, Type: "pattern", Pattern: 1}}
	blueStyleID, _ := f.NewStyle(&blueStyle)
	// grayStyle := excelize.Style{
	// 	Fill: excelize.Fill{Color: []string{"#D9D9D9"}, Type: "pattern", Pattern: 1},
	// 	Font: &excelize.Font{VertAlign: "center"},
	// 	Alignment: &excelize.Alignment{
	// 		Horizontal: "center",
	// 		Vertical:   "center",
	// 	},
	// }
	// grayStyleID, _ := f.NewStyle(grayStyle)
	// if err != nil {
	// 	fmt.Println(err)
	// }
	err := f.Set<PERSON>ellStyle("Sheet1", "B4", "D2", blueStyleID)
	if err = f.SaveAs("sanGuo.xlsx"); err != nil {
		fmt.Println(err)
	}
}
