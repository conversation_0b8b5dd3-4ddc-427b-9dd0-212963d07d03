package main

import (
	"crypto/hmac"
	"crypto/sha1"
	"encoding/hex"
	"fmt"
	Url "net/url"
	"sort"
	"strings"
)

func sign(secret_key, method, url string, params map[string]string, body string) (signStr string, err error) {

	// 1. 拼接签名串

	// params 排序
	keys := make([]string, 0)
	for k, _ := range params {
		keys = append(keys, k)
	}
	sort.Strings(keys)

	paramsPairs := make([]string, 0)

	for _, k := range keys {
		paramsPairs = append(paramsPairs, fmt.Sprintf("%s=%s", k, params[k]))
	}

	paramsStr := strings.Join(paramsPairs, "&")

	rawStr := fmt.Sprintf("%s%s?%s", method, url, paramsStr)

	if (method == "POST" || method == "PUT") && len(body) > 0 {
		rawStr += fmt.Sprintf("&Data=%s", body)
	}

	fmt.Println(rawStr)

	// 2. hmac_sha1 签名

	mac := hmac.New(sha1.New, []byte(secret_key))
	mac.Write([]byte(rawStr))
	hash := mac.Sum(nil)
	b16encoded := hex.EncodeToString(hash)
	signStr = Url.QueryEscape(b16encoded)
	return
}

func main() {
	secretkey := "008717206000b4cfa23477ee8d3d751c"

	params := map[string]string{
		"Action":     "GetUsers",
		"AppId":      "100",
		"SecretId":   "1111",
		"Nonce":      "1208",
		"OpenUserId": "1",
		"OrgId":      "3",
		"Timestamp":  "15592929381",
	}

	// data := `{"ObjectId":111,"UserId":101}`

	url := "127.0.0.1:8002/v1/user"

	signStr, _ := sign(secretkey, "GET", url, params, "")
	fmt.Println(signStr) //	d7fa99a01798d41dafd6394f664675242e2719d5

	signStr, _ = sign(secretkey, "GET", url, params, "")
	fmt.Println(signStr) //	5dd6db112357ccb8d8f6ea63a46c4f19a3e66469
}
